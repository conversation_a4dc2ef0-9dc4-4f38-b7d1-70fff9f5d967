---
- name: Set root dir fact for {{ domain_name }}
  ansible.builtin.set_fact:
    site_root_dir: '/home/<USER>/sites/{{ domain_name }}'

- name: Check if site root directory exists
  ansible.builtin.stat:
    path: '{{ site_root_dir }}'
  register: site_root_exists

- name: Setting up root dir for {{ domain_name }}
  when: not site_root_exists.stat.exists
  ansible.builtin.file:
    path: '{{ item }}'
    state: directory
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '0755'
  loop:
    - '{{ site_root_dir }}'
    - '{{ site_root_dir }}/cache'
    - '{{ site_root_dir }}/logs'
    - '{{ site_root_dir }}/public'
    - '{{ site_root_dir }}/backups'

- name: Check if migration files exist locally
  ansible.builtin.stat:
    path: '{{ item }}'
  register: migration_files_check
  loop:
    - '{{ migration_source_path }}/{{ domain_name }}.zip'
    - '{{ migration_source_path }}/{{ domain_name }}.sql'
  # delegate_to: localhost

- name: Fail if migration files are missing
  ansible.builtin.fail:
    msg: 'Migration file {{ item.item }} not found'
  when: not item.stat.exists
  loop: '{{ migration_files_check.results }}'

- name: Copy migration files to site root directory
  ansible.builtin.copy:
    src: '{{ item }}'
    dest: '{{ site_root_dir }}/'
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '0644'
  loop:
    - '{{ migration_source_path }}/{{ domain_name }}.zip'
    - '{{ migration_source_path }}/{{ domain_name }}.sql'

- name: Extract WordPress files
  ansible.builtin.unarchive:
    src: '{{ site_root_dir }}/{{ domain_name }}.zip'
    dest: '{{ site_root_dir }}/public'
    owner: '{{ user }}'
    group: '{{ user }}'
    remote_src: true
    creates: '{{ site_root_dir }}/public/wp-config.php'

- name: Create DB for {{ domain_name }}
  community.mysql.mysql_db:
    name: '{{ wp_db_name }}'
    state: present
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
  become: true

- name: Create DB user for {{ domain_name }}
  community.mysql.mysql_user:
    name: '{{ wp_db_user }}'
    password: '{{ wp_db_password }}'
    priv: '{{ wp_db_name }}.*:ALL'
    host: localhost
    state: present
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true

- name: Import database
  community.mysql.mysql_db:
    name: '{{ wp_db_name }}'
    state: import
    target: '{{ site_root_dir }}/{{ domain_name }}.sql'
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true

- name: Check if wp-config.php exists
  ansible.builtin.stat:
    path: '{{ site_root_dir }}/public/wp-config.php'
  register: wp_config_exists

- name: Update database credentials in wp-config.php
  ansible.builtin.replace:
    path: '{{ site_root_dir }}/public/wp-config.php'
    regexp: '{{ item.regexp }}'
    replace: '{{ item.replace }}'
  loop:
    - {
        regexp: "define\\(\\s*['\"]DB_NAME['\"]\\s*,\\s*['\"][^'\"]*['\"]\\s*\\);",
        replace: "define( 'DB_NAME', '{{ wp_db_name }}' );",
      }
    - {
        regexp: "define\\(\\s*['\"]DB_USER['\"]\\s*,\\s*['\"][^'\"]*['\"]\\s*\\);",
        replace: "define( 'DB_USER', '{{ wp_db_user }}' );",
      }
    - {
        regexp: "define\\(\\s*['\"]DB_PASSWORD['\"]\\s*,\\s*['\"][^'\"]*['\"]\\s*\\);",
        replace: "define( 'DB_PASSWORD', '{{ wp_db_password }}' );",
      }
    - {
        regexp: "define\\(\\s*['\"]DB_HOST['\"]\\s*,\\s*['\"][^'\"]*['\"]\\s*\\);",
        replace: "define( 'DB_HOST', 'localhost' );",
      }
  when: wp_config_exists.stat.exists

- name: Flush WordPress rewrite rules
  ansible.builtin.command:
    cmd: /usr/local/bin/wp rewrite flush --path={{ site_root_dir }}/public
  register: rewrite_flush_result
  changed_when: rewrite_flush_result.rc == 0

- name: Update WordPress cache if caching plugin is active
  ansible.builtin.command:
    cmd: /usr/local/bin/wp cache flush --path={{ site_root_dir }}/public
  register: cache_flush_result
  failed_when: false
  changed_when: cache_flush_result.rc == 0

- name: Remove default WP_DEBUG block from wp-config.php (if it exists)
  ansible.builtin.replace:
    path: '{{ site_root_dir }}/public/wp-config.php'
    regexp: "^if \\( ! defined\\( 'WP_DEBUG' \\) \\) \\{.*?^\\s*define\\( 'WP_DEBUG', (?:true|false) \\);.*?^\\}(?:\\s*\\n)?$"
    replace: ''
  when: wp_config_exists.stat.exists

- name: Add custom PHP and WordPress settings to wp-config.php
  ansible.builtin.blockinfile:
    path: '{{ site_root_dir }}/public/wp-config.php'
    block: |
      /* Custom PHP Error Logging Configuration */
      @ini_set( 'log_errors', 'On' );
      @ini_set( 'display_errors', 'Off' );
      @ini_set( 'error_log', '{{ site_root_dir }}/logs/php_error.log' );
      /* End Custom PHP Error Logging Configuration */

      /* Custom WordPress Configuration Settings */
      define( 'WP_POST_REVISIONS', 5 );
      define( 'AUTOSAVE_INTERVAL', 300 );
      define( 'DISALLOW_FILE_EDIT', true );
      define( 'FORCE_SSL_ADMIN', true );
      define( 'IMAGE_EDIT_OVERWRITE', true );
      define( 'WP_AUTO_UPDATE_CORE', 'minor' );
      define( 'WP_CACHE', true );
      define( 'WP_CRON_LOCK_TIMEOUT', 60 );
      define( 'WP_DISABLE_FATAL_ERROR_HANDLER', false );
      define( 'WP_DEBUG', true );
      define( 'WP_DEBUG_DISPLAY', false );
      define( 'WP_DEBUG_LOG', '{{ site_root_dir }}/logs/debug.log' );
      /* End Custom WordPress Configuration Settings */
    marker: '// ANSIBLE MANAGED BLOCK for Custom WP Settings'
    insertbefore: "/* That's all, stop editing! Happy publishing. */"
  when: wp_config_exists.stat.exists

- name: Set proper file permissions
  ansible.builtin.file:
    path: '{{ item.path }}'
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '{{ item.mode }}'
    recurse: '{{ item.recurse | default(false) }}'
  loop:
    - { path: '{{ site_root_dir }}/public', mode: '0755', recurse: true }
    - { path: '{{ site_root_dir }}/public/wp-content', mode: '0775' }
    - { path: '{{ site_root_dir }}/public/wp-config.php', mode: '0640' }

- name: Clean up migration files
  ansible.builtin.file:
    path: '{{ item }}'
    state: absent
  loop:
    - '{{ site_root_dir }}/{{ domain_name }}.zip'
    - '{{ site_root_dir }}/{{ domain_name }}.sql'

- name: Test Nginx configuration
  ansible.builtin.command:
    cmd: nginx -t
  become: true
  changed_when: false

- name: Verify WordPress installation
  ansible.builtin.command:
    cmd: /usr/local/bin/wp core verify-checksums --path={{ site_root_dir }}/public
  register: wp_verify_result
  failed_when: false
  changed_when: false

- name: Display migration summary
  ansible.builtin.debug:
    msg: |
      WordPress migration completed for {{ domain_name }}:
      - Site files extracted to: {{ site_root_dir }}/public
      - Database imported to: {{ wp_db_name }}
      - Database credentials updated in wp-config.php
      - File permissions set correctly
      - Migration files cleaned up
