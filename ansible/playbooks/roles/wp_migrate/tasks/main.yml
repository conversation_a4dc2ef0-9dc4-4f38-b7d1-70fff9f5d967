---
- name: Set root dir fact for {{ domain_name }}
  ansible.builtin.set_fact:
    site_root_dir: '/home/<USER>/sites/{{ domain_name }}'
    migration_temp_dir: '/tmp/wp_migrate_{{ domain_name }}'

- name: Check if site root directory exists
  ansible.builtin.stat:
    path: '{{ site_root_dir }}'
  register: site_root_exists

- name: Setting up root dir for {{ domain_name }}
  when: not site_root_exists.stat.exists
  ansible.builtin.file:
    path: '{{ item }}'
    state: directory
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '0755'
  loop:
    - '{{ site_root_dir }}'
    - '{{ site_root_dir }}/cache'
    - '{{ site_root_dir }}/logs'
    - '{{ site_root_dir }}/public'
    - '{{ site_root_dir }}/backups'

- name: Create temporary migration directory
  ansible.builtin.file:
    path: '{{ migration_temp_dir }}'
    state: directory
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '0755'

- name: Check if migration files exist locally
  ansible.builtin.stat:
    path: '{{ item }}'
  register: migration_files_check
  loop:
    - '{{ migration_source_path }}/{{ domain_name }}.zip'
    - '{{ migration_source_path }}/{{ domain_name }}.sql'
  delegate_to: localhost

- name: Fail if migration files are missing
  ansible.builtin.fail:
    msg: "Migration file {{ item.item }} not found"
  when: not item.stat.exists
  loop: '{{ migration_files_check.results }}'

- name: Copy migration files to server
  ansible.builtin.copy:
    src: '{{ item }}'
    dest: '{{ migration_temp_dir }}/'
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '0644'
  loop:
    - '{{ migration_source_path }}/{{ domain_name }}.zip'
    - '{{ migration_source_path }}/{{ domain_name }}.sql'

- name: Extract WordPress files
  ansible.builtin.unarchive:
    src: '{{ migration_temp_dir }}/{{ domain_name }}.zip'
    dest: '{{ site_root_dir }}/public'
    owner: '{{ user }}'
    group: '{{ user }}'
    remote_src: true
    creates: '{{ site_root_dir }}/public/wp-config.php'

- name: Create DB for {{ domain_name }}
  community.mysql.mysql_db:
    name: '{{ wp_db_name }}'
    state: present
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
  become: true

- name: Create DB user for {{ domain_name }}
  community.mysql.mysql_user:
    name: '{{ wp_db_user }}'
    password: '{{ wp_db_password }}'
    priv: '{{ wp_db_name }}.*:ALL'
    host: localhost
    state: present
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true

- name: Import database
  community.mysql.mysql_db:
    name: '{{ wp_db_name }}'
    state: import
    target: '{{ migration_temp_dir }}/{{ domain_name }}.sql'
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true

- name: Check if wp-config.php exists
  ansible.builtin.stat:
    path: '{{ site_root_dir }}/public/wp-config.php'
  register: wp_config_exists

- name: Update database credentials in wp-config.php
  ansible.builtin.replace:
    path: '{{ site_root_dir }}/public/wp-config.php'
    regexp: "{{ item.regexp }}"
    replace: "{{ item.replace }}"
  loop:
    - { regexp: "define\\(\\s*['\"]DB_NAME['\"]\\s*,\\s*['\"][^'\"]*['\"]\\s*\\);", replace: "define( 'DB_NAME', '{{ wp_db_name }}' );" }
    - { regexp: "define\\(\\s*['\"]DB_USER['\"]\\s*,\\s*['\"][^'\"]*['\"]\\s*\\);", replace: "define( 'DB_USER', '{{ wp_db_user }}' );" }
    - { regexp: "define\\(\\s*['\"]DB_PASSWORD['\"]\\s*,\\s*['\"][^'\"]*['\"]\\s*\\);", replace: "define( 'DB_PASSWORD', '{{ wp_db_password }}' );" }
    - { regexp: "define\\(\\s*['\"]DB_HOST['\"]\\s*,\\s*['\"][^'\"]*['\"]\\s*\\);", replace: "define( 'DB_HOST', 'localhost' );" }
  when: wp_config_exists.stat.exists

- name: Update site URL in database
  ansible.builtin.command:
    cmd: >
      /usr/local/bin/wp search-replace
      '{{ old_domain_url | default("https://olddomain.com") }}'
      'https://{{ domain_name }}'
      --path={{ site_root_dir }}/public
      --skip-columns=guid
  register: url_replace_result
  changed_when: url_replace_result.rc == 0

- name: Update WordPress home and site URL options
  ansible.builtin.command:
    cmd: /usr/local/bin/wp option update {{ item }} 'https://{{ domain_name }}' --path={{ site_root_dir }}/public
  loop:
    - home
    - siteurl
  register: option_update_result
  changed_when: option_update_result.rc == 0

- name: Flush WordPress rewrite rules
  ansible.builtin.command:
    cmd: /usr/local/bin/wp rewrite flush --path={{ site_root_dir }}/public
  register: rewrite_flush_result
  changed_when: rewrite_flush_result.rc == 0
