# WordPress Migration Role (wp_migrate)

This Ansible role migrates an existing WordPress site to a new server by importing site files and database from backup files.

## Requirements

- WordPress CLI (wp-cli) installed on the target server
- MySQL/MariaDB server running on the target server
- Nginx web server configured
- Migration files: `{domain_name}.zip` and `{domain_name}.sql`

## Role Variables

### Required Variables

- `domain_name`: The domain name for the WordPress site (e.g., "example.com")
- `user`: The system user that will own the WordPress files
- `wp_db_name`: Database name for the WordPress site
- `wp_db_user`: Database username for WordPress
- `wp_db_password`: Database password for WordPress
- `mysql_root_password`: MySQL root password for database operations
- `mysql_unix_socket_path`: Path to MySQL socket (usually "/var/run/mysqld/mysqld.sock")
- `migration_source_path`: Local path where migration files are stored

### Optional Variables

- `old_domain_url`: The old domain URL to replace in the database (defaults to "https://olddomain.com")

## Migration Files

The role expects two files in the `migration_source_path` directory:

1. `{domain_name}.zip` - Contains the WordPress site files
2. `{domain_name}.sql` - Contains the database dump

## Example Playbook

```yaml
---
- hosts: webservers
  become: yes
  vars:
    domain_name: "newsite.com"
    user: "webuser"
    wp_db_name: "newsite_wp"
    wp_db_user: "newsite_user"
    wp_db_password: "secure_password"
    mysql_root_password: "mysql_root_pass"
    mysql_unix_socket_path: "/var/run/mysqld/mysqld.sock"
    migration_source_path: "/path/to/migration/files"
    old_domain_url: "https://oldsite.com"
  roles:
    - wp_migrate
```

## What This Role Does

1. **Prepares the environment**:
   - Creates site directory structure
   - Sets up temporary migration directory

2. **Transfers migration files**:
   - Copies ZIP and SQL files to the server
   - Validates that required files exist

3. **Extracts and sets up WordPress**:
   - Extracts WordPress files from ZIP archive
   - Creates database and database user
   - Imports database from SQL file

4. **Updates configuration**:
   - Updates database credentials in wp-config.php
   - Performs search-replace for domain URLs
   - Updates WordPress options (home, siteurl)
   - Flushes rewrite rules and cache

5. **Finalizes setup**:
   - Adds custom WordPress configuration
   - Sets proper file permissions
   - Cleans up temporary files
   - Verifies installation

## Directory Structure Created

```
/home/<USER>/sites/{domain_name}/
├── public/          # WordPress files
├── logs/           # Error and debug logs
├── cache/          # Cache directory
└── backups/        # Backup storage
```

## File Permissions

- Site directories: 0755
- wp-content directory: 0775
- wp-config.php: 0640
- All files owned by the specified user

## Notes

- The role will fail if migration files are not found
- Database credentials are automatically updated in wp-config.php
- URL replacement is performed in the database to update domain references
- Custom WordPress security and performance settings are applied
- Temporary migration files are cleaned up after successful migration
