---
- name: Check if Nginx configuration exists
  ansible.builtin.stat:
    path: '/etc/nginx/sites-available/{{ domain_name }}'
  register: nginx_site_config_check
  become: true

- name: Create Nginx configuration for {{ domain_name }}
  ansible.builtin.copy:
    src: '/etc/nginx/sites-available/wp.conf'
    dest: '/etc/nginx/sites-available/{{ domain_name }}'
    owner: root
    group: root
    mode: '0644'
    backup: true
    remote_src: true
  when: not nginx_site_config_check.stat.exists
  register: nginx_config_created
  become: true
  notify: Reload Nginx

- name: Replace placeholders in Nginx config
  ansible.builtin.replace:
    path: '/etc/nginx/sites-available/{{ domain_name }}'
    regexp: '{{ item.regexp }}'
    replace: '{{ item.replace }}'
  loop:
    - { regexp: '\[\[DOMAIN\]\]', replace: '{{ domain_name }}' }
    - { regexp: '\[\[USER\]\]', replace: '{{ user }}' }
  when: not nginx_site_config_check.stat.exists or nginx_config_created.changed
  become: true
  notify: Reload Nginx

- name: Check if Nginx site is already enabled
  ansible.builtin.stat:
    path: '/etc/nginx/sites-enabled/{{ domain_name }}'
  register: nginx_site_enabled_check
  become: true

- name: Enable Nginx site
  ansible.builtin.file:
    src: '/etc/nginx/sites-available/{{ domain_name }}'
    dest: '/etc/nginx/sites-enabled/{{ domain_name }}'
    state: link
  when: not nginx_site_enabled_check.stat.exists
  become: true
  notify: Reload Nginx

- name: Test Nginx configuration
  ansible.builtin.command:
    cmd: nginx -t
  become: true
  changed_when: false
